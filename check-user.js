const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { id: 'cmc1tw1d50000lh6tsll2qspq' },
      select: { id: true, email: true, planType: true }
    });
    
    console.log('User found:', user);
    
    if (!user) {
      console.log('User does not exist in database');
      const userCount = await prisma.user.count();
      console.log('Total users in database:', userCount);
      
      if (userCount > 0) {
        const firstUser = await prisma.user.findFirst({
          select: { id: true, email: true, planType: true }
        });
        console.log('First user in database:', firstUser);
      }
    }
  } catch (error) {
    console.error('Database error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();
